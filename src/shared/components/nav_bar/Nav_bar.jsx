import React, { useState, useEffect } from 'react';
import { NavbarBrand, Navbar, NavItem, Nav } from 'reactstrap';
import { useNavigate, useLocation } from 'react-router-dom';
import NavBarSearch from './Nav_bar_search';
import LoginLogoutButton from './Login_logout_button';
import Menu from './Menu';
// TEMPORARILY DISABLED - Language Menu Import
// import LanguageMenu from './Language_menu';
import { getCookies, clearCookies } from '../../helpers/Cookies';
import { useScrollPosition } from '../../hooks/Scroll_position';
import { clearSessionStorage } from '../../helpers/Session_storage_helper';
// TEMPORARILY DISABLED - Change Language Function Import
// import { changeLanguage } from '../../i18n';

function CustomNavbar({ t, detectLanguage, showmodal }) {
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const token = getCookies('token');
  const [navbarCollapse, setNavbarCollapse] = useState(false);
  const [isHome, setIsHome] = useState(window.location.pathname === '/');
  const scrollPosition = useScrollPosition(isHome);

  const [auth, setAuth] = useState(false);

  const logout = () => {
    toggleNavbarCollapse();
    navigate('/');
    clearCookies();
    clearSessionStorage();
  };

  const toggleNavbarCollapse = () => {
    setNavbarCollapse(!navbarCollapse);
    document.documentElement.classList.toggle('nav-open');
  };

  useEffect(() => {
    setAuth(token);
    if (pathname === '/') {
      setIsHome(true);
    } else {
      setIsHome(false);
    }
  }, [pathname, token]);

  return (
    <Navbar
      className={isHome ? 'sticky-top' : 'mbt40 sticky-top'}
      expand="lg"
      style={
        scrollPosition < 22
          ? {
              backgroundColor: isHome ? 'transparent' : 'white'
            }
          : {}
      }
    >
      <div className="container">
        <div className="row w-100 align-items-center">
          <div className="navbar-translate col-6 col-lg-2">
            <NavbarBrand
              data-placement="bottom"
              style={{ fontWeight: 600 }}
              href="/"
              onClick={() => clearSessionStorage()}
            >
              <span
                style={{
                  backgroundColor: '#ECA869',
                  padding: '1px 1px 0px 5px',
                  borderRadius: '0px 13px'
                }}
              >
                De
              </span>
              rental
            </NavbarBrand>
          </div>

          <div className="col-lg-8 d-lg-inline-block d-none show-search-bar ">
            <NavBarSearch t={t} detectLanguage={detectLanguage} />
          </div>

          <div className="col-6 padding-r-0 text-right col-lg-2">
            <Nav className="align-items-center d-lg-flex justify-content-end">
              <LoginLogoutButton logout={logout} t={t} showmodal={showmodal} />
              {/* TEMPORARILY DISABLED - Language Menu for Multi-language Support
                 TODO: Re-enable when full internationalization is properly configured
                 This includes: translations, RTL support, locale-specific formatting, etc.
              <NavItem>
                <LanguageMenu changeLanguage={changeLanguage} t={t} />
              </NavItem>
              */}
              <NavItem>
                <Menu t={t} token={auth} />
              </NavItem>
            </Nav>
          </div>
        </div>
      </div>
    </Navbar>
  );
}
export default CustomNavbar;
